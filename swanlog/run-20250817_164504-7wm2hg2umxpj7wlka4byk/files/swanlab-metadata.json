{"memory": "582", "cpu": {"brand": "Intel(R) Xeon(R) Gold 5215 CPU @ 2.50GHz", "cores": 40}, "gpu": {"nvidia": {"driver": "535.183.01", "cores": 4, "type": ["NVIDIA GeForce RTX 3090", "NVIDIA GeForce RTX 3090", "NVIDIA GeForce RTX 3090", "NVIDIA GeForce RTX 3090"], "memory": ["24", "24", "24", "24"], "cuda": null, "architecture": ["Ampere", "Ampere", "Ampere", "Ampere"], "cudacores": [10496, 10496, 10496, 10496]}}, "os": "Linux-5.15.0-139-generic-x86_64-with-glibc2.31", "os_pretty_name": null, "hostname": "zju-49", "pid": 996188, "cwd": "/data/zju-49/wangzixuan/workspace/verl-agent", "python": "3.9.23", "python_verbose": "3.9.23 (main, Jun  5 2025, 13:40:20) \n[GCC 11.2.0]", "executable": "/home/<USER>/miniforge3/envs/agentenv-alfworld/bin/python3", "command": "/home/<USER>/.local/lib/python3.9/site-packages/ray/_private/workers/default_worker.py --node-ip-address=************* --node-manager-port=39219 --object-store-name=/tmp/ray/session_2025-08-17_16-42-47_318056_992419/sockets/plasma_store --raylet-name=/tmp/ray/session_2025-08-17_16-42-47_318056_992419/sockets/raylet --redis-address=None --metrics-agent-port=62055 --logging-rotate-bytes=536870912 --logging-rotate-backup-count=5 --runtime-env-agent-port=62669 --gcs-address=*************:40923 --session-name=session_2025-08-17_16-42-47_318056_992419 --temp-dir=/tmp/ray --webui=127.0.0.1:8265 --cluster-id=10013ed25a97c21095e182dd8bc05ed032dea6f94762b108d93becb4 --startup-token=40 --worker-launch-time-ms=1755420172115 --node-id=620e68c00543ceeded4567ab54ded89d81bdd03b8227c42d894e63be --runtime-env-hash=-1624044036 --enable-resource-isolation=false", "git_remote": "https://github.com/langfengQ/verl-agent", "git_info": ["dev", "aea4b907976b30529cbe8a9643f643e621f55002"], "swanlab": {"version": "0.6.8", "_monitor": 5, "logdir": "/data/zju-49/wangzixuan/workspace/verl-agent/swanlog/run-20250817_164504-7wm2hg2umxpj7wlka4byk"}}